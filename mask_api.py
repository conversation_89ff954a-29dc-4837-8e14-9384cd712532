import os
import json
from datetime import datetime
import pandas as pd
from flask import Blueprint, request, jsonify, send_file
from utils1 import  get_file_from_url
from masks_process_http import parent_dir, process_task_data, latest_table_path, processed_monthly_dir, \
    extract_all_monthly_and_annual, mapping_table_path, month_to_quarters
from query_http import extract_task_progress, fill_progress_in_middle_table
import logging
blueprint = Blueprint('mainController_new', __name__, url_prefix='/unicomapi_new')
@blueprint.route('/api/annual_data_process', methods=['POST'])
def api_annual_data_process():
    """
    处理年度数据的Flask接口
    请求参数：
    {
        "annual_input_path": "年度任务文件路径"
    }
    """
    try:
        data = request.get_json()
        annual_input_url = data.get('annual_input_url')
        if annual_input_url:
            annual_input_path, uuid = get_file_from_url(annual_input_url, 'a')
        else:
            return jsonify({"error": "Missing annual_input_url parameter"}), 400
        if not os.path.isfile(annual_input_path):
            return jsonify({"error": "URL is not correct"}), 400

        # 复用原有函数逻辑
        middle_table_name = 'middle_table.xlsx'
        formatted_now = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
        middle_table_output_path_root = os.path.join(parent_dir, rf'middle_table/{formatted_now}')

        if not os.path.exists(middle_table_output_path_root):
            os.makedirs(middle_table_output_path_root)

        if os.path.exists(annual_input_path):
            combined_df = process_task_data(annual_input_path)
        else:
            return jsonify({"error": "年度重点工作任务文件不存在，请上传！"}), 400

        middle_table_output_path = os.path.join(middle_table_output_path_root, middle_table_name)
        combined_df.to_excel(middle_table_output_path, index=False)

        if not os.path.exists(os.path.dirname(latest_table_path)):
            os.makedirs(os.path.dirname(latest_table_path), exist_ok=True)

        with open(latest_table_path, 'w') as f:
            json.dump([middle_table_output_path, annual_input_path], f)

        return {"message": "年度重点工作任务已成功更新到中间表中",
                       "output_path": middle_table_output_path}
    except Exception as e:
        logging.error(e)
        return {"error": str(e)}

@blueprint.route('/api/monthly_data_extract', methods=['POST'])
def api_monthly_data_extract():
    """
    提取月度数据的Flask接口
    """
    try:
        data = request.get_json()
        monthly_input_url = data.get("monthly_input_url")

        if monthly_input_url:
            file_path, uuid = get_file_from_url(monthly_input_url, 'm')
        else:
            return jsonify({"error": "Missing annual_input_url parameter"}), 400

        if not os.path.exists(processed_monthly_dir):
            os.makedirs(processed_monthly_dir)

        with open(latest_table_path, 'r') as f:
            latest_annual_tasks_path = json.load(f)[1]

        data = extract_all_monthly_and_annual(latest_annual_tasks_path)
        return jsonify(data), 200
    except Exception as e:
        logging.error(e)
        return jsonify({"error": str(e)}), 500

@blueprint.route('/api/fill_association', methods=['POST'])
def api_fill_association():
    """
    填充关联数据的Flask接口
    请求参数：
    {
        "assoc_list": [...],
        "month": 3,
        "source_name": "2025年3月月度重点工作计划"
    }
    """
    try:
        data = request.get_json()
        assoc_list = data.get('assoc_list')
        month = data.get('month')
        if month:
            month = int(month)
        source_name = data.get('source_name')
        print("assoc_list:", assoc_list)
        print("source_name:", source_name)
        print("month:", month)
        if not all([assoc_list is not None, month, source_name]):
            return jsonify({"error": "Missing required parameters: assoc_list, month, source_name"}), 400

        with open(latest_table_path, 'r') as f:
            middle_table_path = json.load(f)[0]

        with open(mapping_table_path, 'r') as f:
            monthly_plan_path = json.load(f)[source_name]

        quarter = month_to_quarters(month)[0]

        # 读取中间表
        df = pd.read_excel(middle_table_path)
        # 读取月度计划表
        df_monthly = pd.read_excel(monthly_plan_path)

        if '原表序号' in df.columns:
            df['原表序号'] = df['原表序号'].astype(str)
        else:
            return {"error": "新表缺少原表序号列"}

        annual_idx_map = {str(row['原表序号']): idx for idx, row in df.iterrows() if
                          row['原表序号'].isdigit() and int(row['原表序号']) <= 66}

        # 关联填充
        for item in assoc_list:
            if '年度重点工作任务序号' in item:
                seq = str(item['年度重点工作任务序号'])
                idx = annual_idx_map.get(seq)
                if idx is not None:
                    col_target = f"{quarter}季度关联任务目标"
                    col_deliver = f"{quarter}季度关联交付物"
                    # 只填充当前季度（可根据实际需求调整）
                    if item.get('关联任务目标'):
                        old_target_val = df.at[idx, col_target]
                        if pd.isna(old_target_val):
                            old_target_val = ''
                        else:
                            old_target_val = str(old_target_val)
                        cur_target_id = len(old_target_val.split('\n'))
                        assoc_target = item['关联任务目标'].replace('\n', '')
                        df.at[
                            idx, col_target] = old_target_val + f"{cur_target_id}.{assoc_target}（任务来源：{source_name}；关联理由：{item.get('关联理由', '')}）\n"
                    if item.get('关联交付物'):
                        old_deliver_val = df.at[idx, col_deliver]
                        if pd.isna(old_deliver_val):
                            old_deliver_val = ''
                        else:
                            old_deliver_val = str(old_deliver_val)
                        cur_deliver_id = len(old_deliver_val.split('\n'))
                        assoc_deliver = item['关联交付物'].replace('\n', '')
                        df.at[
                            idx, col_deliver] = old_deliver_val + f"{cur_deliver_id}.{assoc_deliver} 时间节点：{item.get('时间节点要求', '')}（任务来源：{source_name}；关联理由：{item.get('关联理由', '')}）\n"
            # 非关联数据，按source_name和月度计划序号追加
            elif '月度计划序号' in item:
                seq = item['月度计划序号']
                # 查找月度计划表唯一序号对应的数据
                rec = df_monthly[df_monthly['序号'] == seq]
                if not rec.empty:
                    max_serial = df['序号'].max() if '序号' in df.columns else 0
                    new_row = {col: '' for col in df.columns}
                    new_row['序号'] = max_serial + 1
                    new_row['原表序号'] = rec.iloc[0]['序号']
                    new_row['任务'] = rec.iloc[0]['事项']
                    new_row[f'{quarter}季度进度目标'] = rec.iloc[0]['计划安排时间']
                    new_row['任务来源'] = source_name
                    new_row['责任部门'] = rec.iloc[0]['牵头部门'] if '牵头部门' in rec.columns and not pd.isna(
                        rec.iloc[0]['牵头部门']) else ''
                    new_row['配合部门'] = rec.iloc[0]['协办部门'] if '协办部门' in rec.columns and not pd.isna(
                        rec.iloc[0]['协办部门']) else ''
                    new_row['完成状态'] = '未完成'
                    # 其他字段可按需补充
                    df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)

        # 重新编号
        df = df.reset_index(drop=True)
        df['序号'] = range(1, len(df) + 1)

        # 保存
        df.to_excel(middle_table_path, index=False)
        return {"message":source_name}

    except Exception as e:
        logging.error(e)

        return {"error": str(e)}

@blueprint.route('/api/extract_task_progress', methods=['POST'])
def extract_task_progress_api():
    req = request.get_json()
    monthly_input_url = req.get("monthly_input_url", [])
    month = req.get('month')
    if month:
        month = int(month)
    if monthly_input_url:
        report_paths = []
        for url in monthly_input_url:
            file_path, uuid = get_file_from_url(url, 'r')
            report_paths.append(file_path)
        result = extract_task_progress(report_paths, month)
        return jsonify(result)
    else:
        return {"message":"monthly_input_url is null"}

@blueprint.route('/api/fill_progress_in_middle_table', methods=['POST'])
def fill_progress_in_middle_table_api():
    req = request.get_json()
    tasks_progress_list = req.get('tasks_progress_list', [])
    month = req.get('month')
    department = req.get('department')
    result = fill_progress_in_middle_table(tasks_progress_list, month, department)
    return {"result":result}

@blueprint.route("/getfileurl/<string:path1>/<string:filename>", methods=['GET'])
def download_files(path1, filename):
    # 假设文件存储在某个目录下，例如：'./downloads/'
    file_directory = './middle_table/'

    # 拼接文件路径
    file_path = os.path.join(file_directory, path1, filename)

    # 检查文件是否存在
    if not os.path.isfile(file_path):
        return jsonify({"error": "文件不存在"}), 404

    # 使用 send_file 发送文件给客户端
    return send_file(file_path, as_attachment=True)
